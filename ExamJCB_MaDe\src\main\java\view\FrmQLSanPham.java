/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JFrame.java to edit this template
 */
package view;

import dao.SanPhamDAO;
import model.SanPham;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FrmQLSanPham extends javax.swing.JFrame {

    private SanPhamDAO sanPhamDAO;
    private DefaultTableModel tableModel;

    // Khai báo các thành phần giao diện
    private JTextField txtTenSP;
    private JTextField txtGia;
    private JRadioButton rdoConHang;
    private JRadioButton rdoHetHang;
    private ButtonGroup buttonGroup;
    private JTable tblSanPham;
    private JButton btnThem;
    private JButton btnSua;
    private JButton btnXoa;
    private JButton btnNhapMoi;
    private JTextField txtTimKiem;
    private JButton btnTimKiem;
    private JComboBox<String> cboTrangThai;

    /**
     * Creates new form FrmQLSanPham
     */
    public FrmQLSanPham() {
        sanPhamDAO = new SanPhamDAO();
        initComponents();
        loadData();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     */
    @SuppressWarnings("unchecked")
    private void initComponents() {
        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
        setTitle("Giao diện quản lý sản phẩm");
        setSize(800, 600);
        setLocationRelativeTo(null);

        // Tạo layout chính
        setLayout(new BorderLayout());

        // Panel nhập liệu
        JPanel pnlInput = createInputPanel();
        add(pnlInput, BorderLayout.NORTH);

        // Panel bảng dữ liệu
        JPanel pnlTable = createTablePanel();
        add(pnlTable, BorderLayout.CENTER);

        // Panel tìm kiếm và lọc
        JPanel pnlSearch = createSearchPanel();
        add(pnlSearch, BorderLayout.SOUTH);

        // Thêm sự kiện
        addEventHandlers();
    }

    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin sản phẩm"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // Tên sản phẩm
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("Tên sản phẩm:"), gbc);
        gbc.gridx = 1;
        txtTenSP = new JTextField(20);
        panel.add(txtTenSP, gbc);

        // Giá
        gbc.gridx = 2; gbc.gridy = 0;
        panel.add(new JLabel("Giá:"), gbc);
        gbc.gridx = 3;
        txtGia = new JTextField(15);
        panel.add(txtGia, gbc);

        // Trạng thái
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("Trạng thái:"), gbc);

        JPanel pnlRadio = new JPanel(new FlowLayout(FlowLayout.LEFT));
        rdoConHang = new JRadioButton("Còn hàng", true);
        rdoHetHang = new JRadioButton("Hết hàng");
        buttonGroup = new ButtonGroup();
        buttonGroup.add(rdoConHang);
        buttonGroup.add(rdoHetHang);
        pnlRadio.add(rdoConHang);
        pnlRadio.add(rdoHetHang);

        gbc.gridx = 1; gbc.gridwidth = 3;
        panel.add(pnlRadio, gbc);

        // Các nút chức năng
        JPanel pnlButtons = new JPanel(new FlowLayout());
        btnThem = new JButton("Thêm");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnNhapMoi = new JButton("Nhập mới");

        pnlButtons.add(btnThem);
        pnlButtons.add(btnSua);
        pnlButtons.add(btnXoa);
        pnlButtons.add(btnNhapMoi);

        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4;
        panel.add(pnlButtons, gbc);

        return panel;
    }

    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Danh sách sản phẩm"));

        // Tạo bảng
        String[] columnNames = {"Mã SP", "Tên SP", "Giá", "Trạng thái"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho phép chỉnh sửa trực tiếp trên bảng
            }
        };

        tblSanPham = new JTable(tableModel);
        tblSanPham.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Thêm sự kiện click vào bảng
        tblSanPham.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = tblSanPham.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedRowToForm(selectedRow);
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(tblSanPham);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Tìm kiếm và lọc"));

        // Tìm kiếm theo tên
        panel.add(new JLabel("Tìm theo tên:"));
        txtTimKiem = new JTextField(15);
        panel.add(txtTimKiem);
        btnTimKiem = new JButton("Tìm");
        panel.add(btnTimKiem);

        // Lọc theo trạng thái
        panel.add(new JLabel("Lọc theo trạng thái:"));
        cboTrangThai = new JComboBox<>(new String[]{"Tất cả", "Còn hàng", "Hết hàng"});
        panel.add(cboTrangThai);

        return panel;
    }

    private void addEventHandlers() {
        // Sự kiện nút Thêm
        btnThem.addActionListener(e -> themSanPham());

        // Sự kiện nút Sửa
        btnSua.addActionListener(e -> suaSanPham());

        // Sự kiện nút Xóa
        btnXoa.addActionListener(e -> xoaSanPham());

        // Sự kiện nút Nhập mới
        btnNhapMoi.addActionListener(e -> nhapMoi());

        // Sự kiện nút Tìm kiếm
        btnTimKiem.addActionListener(e -> timKiemSanPham());

        // Sự kiện ComboBox lọc trạng thái
        cboTrangThai.addActionListener(e -> locTheoTrangThai());
    }

    private void loadData() {
        tableModel.setRowCount(0);
        List<SanPham> list = sanPhamDAO.getAll();
        for (SanPham sp : list) {
            Object[] row = {sp.getMaSP(), sp.getTenSP(), sp.getGia(), sp.getTrangThai()};
            tableModel.addRow(row);
        }
    }

    private void loadSelectedRowToForm(int selectedRow) {
        if (selectedRow >= 0) {
            txtTenSP.setText(tableModel.getValueAt(selectedRow, 1).toString());
            txtGia.setText(tableModel.getValueAt(selectedRow, 2).toString());
            String trangThai = tableModel.getValueAt(selectedRow, 3).toString();
            if ("Còn hàng".equals(trangThai)) {
                rdoConHang.setSelected(true);
            } else {
                rdoHetHang.setSelected(true);
            }
        }
    }

    private void themSanPham() {
        try {
            String tenSP = txtTenSP.getText().trim();
            if (tenSP.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Vui lòng nhập tên sản phẩm!");
                return;
            }

            double gia = Double.parseDouble(txtGia.getText().trim());
            String trangThai = rdoConHang.isSelected() ? "Còn hàng" : "Hết hàng";

            SanPham sp = new SanPham(tenSP, gia, trangThai);
            sanPhamDAO.insert(sp);

            JOptionPane.showMessageDialog(this, "Thêm sản phẩm thành công!");
            loadData();
            nhapMoi();

        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this, "Giá phải là số!");
        } catch (Exception ex) {
            JOptionPane.showMessageDialog(this, "Lỗi: " + ex.getMessage());
        }
    }

    private void suaSanPham() {
        int selectedRow = tblSanPham.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn sản phẩm cần sửa!");
            return;
        }

        try {
            int maSP = (Integer) tableModel.getValueAt(selectedRow, 0);
            String tenSP = txtTenSP.getText().trim();
            if (tenSP.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Vui lòng nhập tên sản phẩm!");
                return;
            }

            double gia = Double.parseDouble(txtGia.getText().trim());
            String trangThai = rdoConHang.isSelected() ? "Còn hàng" : "Hết hàng";

            SanPham sp = new SanPham(maSP, tenSP, gia, trangThai);
            sanPhamDAO.update(sp);

            JOptionPane.showMessageDialog(this, "Cập nhật sản phẩm thành công!");
            loadData();
            nhapMoi();

        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this, "Giá phải là số!");
        } catch (Exception ex) {
            JOptionPane.showMessageDialog(this, "Lỗi: " + ex.getMessage());
        }
    }

    private void xoaSanPham() {
        int selectedRow = tblSanPham.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn sản phẩm cần xóa!");
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa sản phẩm này?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            try {
                int maSP = (Integer) tableModel.getValueAt(selectedRow, 0);
                sanPhamDAO.delete(maSP);

                JOptionPane.showMessageDialog(this, "Xóa sản phẩm thành công!");
                loadData();
                nhapMoi();

            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this, "Lỗi: " + ex.getMessage());
            }
        }
    }

    private void nhapMoi() {
        txtTenSP.setText("");
        txtGia.setText("");
        rdoConHang.setSelected(true);
        tblSanPham.clearSelection();
        txtTenSP.requestFocus();
    }

    private void timKiemSanPham() {
        String keyword = txtTimKiem.getText().trim();
        if (keyword.isEmpty()) {
            loadData();
            return;
        }

        tableModel.setRowCount(0);
        List<SanPham> list = sanPhamDAO.searchByName(keyword);
        for (SanPham sp : list) {
            Object[] row = {sp.getMaSP(), sp.getTenSP(), sp.getGia(), sp.getTrangThai()};
            tableModel.addRow(row);
        }

        if (list.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Không tìm thấy sản phẩm nào!");
        }
    }

    private void locTheoTrangThai() {
        String selectedTrangThai = (String) cboTrangThai.getSelectedItem();

        if ("Tất cả".equals(selectedTrangThai)) {
            loadData();
        } else {
            tableModel.setRowCount(0);
            List<SanPham> list = sanPhamDAO.filterByTrangThai(selectedTrangThai);
            for (SanPham sp : list) {
                Object[] row = {sp.getMaSP(), sp.getTenSP(), sp.getGia(), sp.getTrangThai()};
                tableModel.addRow(row);
            }
        }
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        // Set Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }

        SwingUtilities.invokeLater(() -> {
            FrmQLSanPham frmSanPham = new FrmQLSanPham();
            frmSanPham.setVisible(true);
        });
    }
}
